# PowerPagesCustomAuth System Overview

## Overview
Password management within Power Pages, using Entra External ID for primary authentication. Enforces a 12-password history compliance by intercepting standard password operations (registration, reset) with a custom backend built on Azure Functions.

Setting a configuration value for password history is not a default feature of Entra External ID, so we are intercepting operations at key points. We are required to create custom Registration / Password Reset scenarios due to our limited access with password operations in Entra External ID. Graph API enables us to perform necessary Entra External ID actions while also using our Azure Functions for password history.

## Components & Roles

**Power Pages (Frontend)**: Provides the custom UI for registration, password reset, and login. JavaScript handles client-side validation and API calls to the Azure Functions backend.

**Entra External ID (Identity Provider)**: Handles standard user sign-in and session management via native Power Pages integration (`/.auth/login/EntraExternalID`).

**Azure Functions (Backend Logic)**: Manages all custom password logic through 4 specialized services:
- **PasswordService**: Handles password validation, history enforcement, and reset operations
- **RegistrationService**: Manages new user registration with invitation validation
- **InvitationService**: Handles user invitation workflow with secure tokens
- **UtilityService**: Provides system health checks, maintenance, and notifications

**Azure Blob Storage (Data Storage)**: Stores the 12-password history for each user in an encrypted JSON format, with data isolation between Power Pages applications.

**Microsoft Graph API (User Management)**: Used by the Azure Functions to create and update user accounts and passwords in the Entra External ID tenant (v1.0 for production stability).

**SendGrid (Email Service)**: Integrated for sending emails using dynamic templates for password reset, invitations, and notifications with Osler branding.

## User Authentication Workflows

### 1. Standard Login
A user navigates to the Power Pages application and is redirected to the standard Entra External ID login page.
Entra External ID validates the user's credentials.
Upon success, the user is redirected back to Power Pages with an authenticated session.

### 2. Custom Registration
A new user fills out the custom registration form on Power Pages using an invitation code.
The form submits the user's details to the RegistrationService Azure Function API.
The Azure Function:
1. Validates the invitation token and verification code.
2. Validates the password complexity.
3. Creates the user in Entra External ID via the Microsoft Graph API.
4. Stores the initial password hash in the Azure Blob Storage history file.

### 3. Password Reset (Logged-In User)
An authenticated user accesses the "Change Password" page.
The user provides their current and new passwords.
PasswordService Azure Function:
1. Verifies the current password.
2. Validates the new password against the 12-entry history in Azure Blob Storage.
3. If valid, updates the password in Entra External ID via the Graph API.
4. Adds the new password hash to the history file in Blob Storage.
The user is logged out of all sessions and prompted to login with the new password.

### 4. Forgot Password (Logged-Out User)
A user enters their email into the public "Forgot Password" form.
The PasswordService Azure Function triggers SendGrid to send a reset link with verification code.
The user clicks the link and is taken to a custom Power Pages form to enter a new password.
The Azure Function validates the new password against the history and updates the user's account via the Graph API.
A confirmation email is sent upon successful reset and the user is redirected to login with their new password.

## Security

**12-Password History Compliance**: Prevents password reuse across all change/reset operations. BCrypt hashing is used for secure password storage.

**Application Isolation**: Password history is stored in a way that segregates user data per Power Pages application using the Department field in Entra External ID.

**Backend Security**: API keys and connection strings for backend services are managed securely in Azure Key Vault with RBAC-based access control.

**Session Invalidation**: User sessions are automatically terminated after a password reset to prevent unauthorized access.

**Production Stability**: Migrated from the Graph API beta to v1.0 for production stability and reliability.

**Authorization**: Azure Functions use Function-level authorization with secure function keys for API access protection.

## Notes

**Select Documentation References**
- Entra External ID
- Power Pages Authentication
- Azure Functions
- Microsoft Graph API

**Current Implementation Status**:
- 4-Function Architecture: PasswordService, RegistrationService, InvitationService, UtilityService
- SendGrid dynamic templates for all email communications
- Azure Key Vault integration for secure configuration management
- Production-ready with comprehensive logging and monitoring
- Modern Azure Functions v4 with resilience patterns
